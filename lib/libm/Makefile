#	$Id: <PERSON><PERSON><PERSON>,v 1.1.1.1 2006/09/14 01:59:06 root Exp $ */
#
#
# Copyright (c) 2001 Opsycon AB  (www.opsycon.se)
# Copyright (c) 2002 <PERSON><PERSON>  (www.lindergren.com)
# 
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions
# are met:
# 1. Redistributions of source code must retain the above copyright
#    notice, this list of conditions and the following disclaimer.
# 2. Redistributions in binary form must reproduce the above copyright
#    notice, this list of conditions and the following disclaimer in the
#    documentation and/or other materials provided with the distribution.
# 3. All advertising materials mentioning features or use of this software
#    must display the following acknowledgement:
#	This product includes software developed by Opsycon AB, Sweden.
#	This product includes software developed by <PERSON><PERSON>.
# 4. The name of the author may not be used to endorse or promote products
#    derived from this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS
# OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
# WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
# ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
# DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
# DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
# OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
# HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
# LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
# OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
# SUCH DAMAGE.
#
#

LIB=m
NOPIC=

MACHINE=	${XMACHINE}
MACHINE_ARCH=	${XMACHINE_ARCH}
CURDIR=$(shell pwd)

M=	${CURDIR}/arch/${MACHINE_ARCH}

CPPFLAGS=	-I$M ${MLIBCPPFLAGS} -U_KERNEL

VPATH+=	${M} ${MLIBDIR}
 
include ${M}/Makefile.inc

# Files to clean up
CLEANFILES+= ${OBJDIR}/lib${LIB}.a

include ${PMONDIR}/tools/scripts/pmon.lib.gmk

${OBJDIR}/lib${LIB}.a: ${OBJS}
	@echo building standard ${LIB} library
	@rm -f $@
	@${AR} rcs $@ ${OBJS}
#	${RANLIB} $@

# DO NOT DELETE

gunzip.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/zlib.h
gunzip.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/stdlib.h
gunzip.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
gunzip.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
gunzip.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
gunzip.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
gunzip.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
gunzip.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
gunzip.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/ubi_uboot.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/div64.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/linux/bitops.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/linux/types.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/ubifs_pmon.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/stddef.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/linux/types.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/linux/crc32.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/linux/list.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/linux/poison.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/linux/rbtree.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/linux/string.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/linux/ubi.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/linux/ubi-user.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/linux/mtd/mtd.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/linux/mtd/compatmac.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/linux/module.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/param.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/simplelock.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/syslimits.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/signal.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/signal.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cpu.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/psl.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/siginfo.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/time.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/time.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/param.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/limits.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/syslog.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/pio.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/intr.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/dev/pci/pcivar.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/bus.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/dev/pci/pcireg.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/pci/pci_machdep.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/target/pmon_target.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/target/bonito.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/cpu.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/nand.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/pmon.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/malloc.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/pmon_arch.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/progress.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/linux/mtd/errno.h
rbtree.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/onenand_uboot.h
ctype.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/linux/ctype.h
cfun.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/linux/ctype.h
cfun.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
cfun.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
cfun.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
cfun.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
cfun.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
cfun.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
cfun.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
lzo1x_decompress.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/linux/lzo.h
lzo1x_decompress.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
lzo1x_decompress.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
lzo1x_decompress.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
lzo1x_decompress.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
lzo1x_decompress.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
lzo1x_decompress.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
lzo1x_decompress.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
lzo1x_decompress.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/linux/byteorder/little_endian.h
lzo1x_decompress.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/linux/byteorder/swab.h
lzo1x_decompress.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/linux/kernel.h
lzo1x_decompress.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/linux/byteorder/generic.h
lzo1x_decompress.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/unaligned.h
lzo1x_decompress.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/linux/types.h
lzo1x_decompress.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/linux/unaligned/le_byteshift.h
lzo1x_decompress.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/linux/unaligned/be_byteshift.h
lzo1x_decompress.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/linux/unaligned/generic.h
lzo1x_decompress.o: lzodefs.h
div64.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
div64.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
div64.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
div64.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
div64.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
div64.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
div64.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
w_acos.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
w_acos.o: math_private.h
w_acos.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
w_acos.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
w_acos.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
w_acos.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
w_acos.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
w_acos.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
w_acos.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
w_acos.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
w_asin.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
w_asin.o: math_private.h
w_asin.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
w_asin.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
w_asin.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
w_asin.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
w_asin.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
w_asin.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
w_asin.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
w_asin.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
s_atan.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
s_atan.o: math_private.h
s_atan.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
s_atan.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
s_atan.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
s_atan.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
s_atan.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
s_atan.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
s_atan.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
s_atan.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
w_atan2.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
w_atan2.o: math_private.h
w_atan2.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
w_atan2.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
w_atan2.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
w_atan2.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
w_atan2.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
w_atan2.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
w_atan2.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
w_atan2.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
s_ceil.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
s_ceil.o: math_private.h
s_ceil.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
s_ceil.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
s_ceil.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
s_ceil.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
s_ceil.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
s_ceil.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
s_ceil.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
s_ceil.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
s_cos.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
s_cos.o: math_private.h
s_cos.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
s_cos.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
s_cos.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
s_cos.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
s_cos.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
s_cos.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
s_cos.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
s_cos.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
w_cosh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
w_cosh.o: math_private.h
w_cosh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
w_cosh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
w_cosh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
w_cosh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
w_cosh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
w_cosh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
w_cosh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
w_cosh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
w_exp.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
w_exp.o: math_private.h
w_exp.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
w_exp.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
w_exp.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
w_exp.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
w_exp.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
w_exp.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
w_exp.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
w_exp.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
s_expm1.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
s_expm1.o: math_private.h
s_expm1.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
s_expm1.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
s_expm1.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
s_expm1.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
s_expm1.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
s_expm1.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
s_expm1.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
s_expm1.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
s_fabs.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
s_fabs.o: math_private.h
s_fabs.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
s_fabs.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
s_fabs.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
s_fabs.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
s_fabs.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
s_fabs.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
s_fabs.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
s_fabs.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
s_floor.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
s_floor.o: math_private.h
s_floor.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
s_floor.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
s_floor.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
s_floor.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
s_floor.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
s_floor.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
s_floor.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
s_floor.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
w_fmod.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
w_fmod.o: math_private.h
w_fmod.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
w_fmod.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
w_fmod.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
w_fmod.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
w_fmod.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
w_fmod.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
w_fmod.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
w_fmod.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
s_frexp.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
s_frexp.o: math_private.h
s_frexp.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
s_frexp.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
s_frexp.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
s_frexp.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
s_frexp.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
s_frexp.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
s_frexp.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
s_frexp.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
w_log.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
w_log.o: math_private.h
w_log.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
w_log.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
w_log.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
w_log.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
w_log.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
w_log.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
w_log.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
w_log.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
w_log10.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
w_log10.o: math_private.h
w_log10.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
w_log10.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
w_log10.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
w_log10.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
w_log10.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
w_log10.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
w_log10.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
w_log10.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
s_modf.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
s_modf.o: math_private.h
s_modf.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
s_modf.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
s_modf.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
s_modf.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
s_modf.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
s_modf.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
s_modf.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
s_modf.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
w_pow.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
w_pow.o: math_private.h
w_pow.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
w_pow.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
w_pow.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
w_pow.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
w_pow.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
w_pow.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
w_pow.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
w_pow.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
s_sin.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
s_sin.o: math_private.h
s_sin.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
s_sin.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
s_sin.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
s_sin.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
s_sin.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
s_sin.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
s_sin.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
s_sin.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
w_sinh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
w_sinh.o: math_private.h
w_sinh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
w_sinh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
w_sinh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
w_sinh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
w_sinh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
w_sinh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
w_sinh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
w_sinh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
w_sqrt.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
w_sqrt.o: math_private.h
w_sqrt.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
w_sqrt.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
w_sqrt.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
w_sqrt.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
w_sqrt.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
w_sqrt.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
w_sqrt.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
w_sqrt.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
s_tan.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
s_tan.o: math_private.h
s_tan.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
s_tan.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
s_tan.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
s_tan.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
s_tan.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
s_tan.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
s_tan.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
s_tan.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
s_tanh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
s_tanh.o: math_private.h
s_tanh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
s_tanh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
s_tanh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
s_tanh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
s_tanh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
s_tanh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
s_tanh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
s_tanh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
s_scalbn.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
s_scalbn.o: math_private.h
s_scalbn.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
s_scalbn.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
s_scalbn.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
s_scalbn.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
s_scalbn.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
s_scalbn.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
s_scalbn.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
s_scalbn.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
s_copysign.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
s_copysign.o: math_private.h
s_copysign.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
s_copysign.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
s_copysign.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
s_copysign.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
s_copysign.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
s_copysign.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
s_copysign.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
s_copysign.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
e_acos.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
e_acos.o: math_private.h
e_acos.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
e_acos.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
e_acos.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
e_acos.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
e_acos.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
e_acos.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
e_acos.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
e_acos.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
e_asin.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
e_asin.o: math_private.h
e_asin.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
e_asin.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
e_asin.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
e_asin.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
e_asin.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
e_asin.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
e_asin.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
e_asin.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
e_atan2.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
e_atan2.o: math_private.h
e_atan2.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
e_atan2.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
e_atan2.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
e_atan2.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
e_atan2.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
e_atan2.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
e_atan2.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
e_atan2.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
k_cos.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
k_cos.o: math_private.h
k_cos.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
k_cos.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
k_cos.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
k_cos.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
k_cos.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
k_cos.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
k_cos.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
k_cos.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
e_cosh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
e_cosh.o: math_private.h
e_cosh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
e_cosh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
e_cosh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
e_cosh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
e_cosh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
e_cosh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
e_cosh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
e_cosh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
e_exp.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
e_exp.o: math_private.h
e_exp.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
e_exp.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
e_exp.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
e_exp.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
e_exp.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
e_exp.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
e_exp.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
e_exp.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
e_fmod.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
e_fmod.o: math_private.h
e_fmod.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
e_fmod.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
e_fmod.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
e_fmod.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
e_fmod.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
e_fmod.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
e_fmod.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
e_fmod.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
e_log.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
e_log.o: math_private.h
e_log.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
e_log.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
e_log.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
e_log.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
e_log.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
e_log.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
e_log.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
e_log.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
e_log10.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
e_log10.o: math_private.h
e_log10.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
e_log10.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
e_log10.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
e_log10.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
e_log10.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
e_log10.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
e_log10.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
e_log10.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
e_pow.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
e_pow.o: math_private.h
e_pow.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
e_pow.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
e_pow.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
e_pow.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
e_pow.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
e_pow.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
e_pow.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
e_pow.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
k_sin.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
k_sin.o: math_private.h
k_sin.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
k_sin.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
k_sin.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
k_sin.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
k_sin.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
k_sin.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
k_sin.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
k_sin.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
e_sinh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
e_sinh.o: math_private.h
e_sinh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
e_sinh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
e_sinh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
e_sinh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
e_sinh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
e_sinh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
e_sinh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
e_sinh.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
e_sqrt.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
e_sqrt.o: math_private.h
e_sqrt.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
e_sqrt.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
e_sqrt.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
e_sqrt.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
e_sqrt.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
e_sqrt.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
e_sqrt.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
e_sqrt.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
k_tan.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
k_tan.o: math_private.h
k_tan.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
k_tan.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
k_tan.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
k_tan.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
k_tan.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
k_tan.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
k_tan.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
k_tan.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
e_rem_pio2.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
e_rem_pio2.o: math_private.h
e_rem_pio2.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
e_rem_pio2.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
e_rem_pio2.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
e_rem_pio2.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
e_rem_pio2.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
e_rem_pio2.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
e_rem_pio2.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
e_rem_pio2.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
k_rem_pio2.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/include/math.h
k_rem_pio2.o: math_private.h
k_rem_pio2.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/types.h
k_rem_pio2.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/types.h
k_rem_pio2.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/cdefs.h
k_rem_pio2.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/cdefs.h
k_rem_pio2.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/ansi.h
k_rem_pio2.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/Targets/ls2k/compile/ls2p500/machine/endian.h
k_rem_pio2.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/sys/endian.h
k_rem_pio2.o: /home/<USER>/share/2p0500/pmon-loongarch-2p500/sys/arch/loongarch/include/endian.h
