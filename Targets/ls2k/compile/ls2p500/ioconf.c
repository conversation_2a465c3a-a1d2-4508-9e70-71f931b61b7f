/*
 * MACHINE GENERATED: DO NOT EDIT
 *
 * ioconf.c, from "ls2p500"
 */

#include "mainbus.h"
#if NMAINBUS > 0
#include <sys/param.h>
#include <sys/device.h>

extern struct cfdriver mainbus_cd;
extern struct cfdriver pcibr_cd;
extern struct cfdriver usb_cd;
extern struct cfdriver loopdev_cd;
extern struct cfdriver localbus_cd;
extern struct cfdriver emmc_cd;
extern struct cfdriver tfcard_cd;
extern struct cfdriver syn_cd;
extern struct cfdriver pci_cd;
extern struct cfdriver pciide_cd;
extern struct cfdriver ppb_cd;
extern struct cfdriver ahci_cd;
extern struct cfdriver ahci_sd_cd;
extern struct cfdriver ahci_cdrom_cd;
extern struct cfdriver lohci_cd;
extern struct cfdriver wd_cd;

extern struct cfattach mainbus_ca;
extern struct cfattach pcibr_ca;
extern struct cfattach usb_ca;
extern struct cfattach loopdev_ca;
extern struct cfattach localbus_ca;
extern struct cfattach emmc_ca;
extern struct cfattach tfcard_ca;
extern struct cfattach syn_ca;
extern struct cfattach pci_ca;
extern struct cfattach pciide_ca;
extern struct cfattach ppb_ca;
extern struct cfattach ahci_ca;
extern struct cfattach ahci_sd_ca;
extern struct cfattach ahci_cdrom_ca;
extern struct cfattach lohci_ca;
extern struct cfattach wd_ca;


/* locators */
static int loc[5] = {
	-1, -1, 0x14140000, 0x14030000, 0x14038000,
};

#ifndef MAXEXTRALOC
#define MAXEXTRALOC 32
#endif
int extraloc[MAXEXTRALOC];
int nextraloc = MAXEXTRALOC;
int uextraloc = 0;

char *locnames[] = {
	"base",
	"bus",
	"dev",
	"function",
	"channel",
	"drive",
};

/* each entry is an index into locnames[]; -1 terminates */
short locnamp[] = {
	-1, 0, -1, 1, -1, 1, -1, 2,
	3, -1, 4, 5, -1,
};

/* size of parent vectors */
int pv_size = 16;

/* parent vectors */
short pv[16] = {
	1, 10, -1, 14, 15, -1, 8, -1, 9, -1, 4, -1, 0, -1, 11, -1,
};

#define NORM FSTATE_NOTFOUND
#define STAR FSTATE_STAR
#define DNRM FSTATE_DNOTFOUND
#define DSTR FSTATE_DSTAR

struct cfdata cfdata[] = {
    /* attachment       driver        unit  state loc     flags parents nm ivstubs starunit1 */
/*  0: mainbus0 at root */
    {&mainbus_ca,	&mainbus_cd,	 0, NORM,     loc,    0, pv+ 2, 0, 0,    0},
/*  1: pcibr* at mainbus0 */
    {&pcibr_ca,		&pcibr_cd,	 0, STAR,     loc,    0, pv+12, 0, 0,    0},
/*  2: usb* at lohci1|lohci0 */
    {&usb_ca,		&usb_cd,	 0, STAR,     loc,    0, pv+ 3, 0, 0,    0},
/*  3: loopdev0 at mainbus0 */
    {&loopdev_ca,	&loopdev_cd,	 0, NORM,     loc,    0, pv+12, 0, 0,    0},
/*  4: localbus0 at mainbus0 */
    {&localbus_ca,	&localbus_cd,	 0, NORM,     loc,    0, pv+12, 0, 0,    0},
/*  5: emmc0 at localbus0 base -1 */
    {&emmc_ca,		&emmc_cd,	 0, NORM, loc+  1,    0, pv+10, 1, 0,    0},
/*  6: tfcard0 at localbus0 base -1 */
    {&tfcard_ca,	&tfcard_cd,	 0, NORM, loc+  1,    0, pv+10, 1, 0,    0},
/*  7: syn0 at localbus0 base 0x14140000 */
    {&syn_ca,		&syn_cd,	 0, NORM, loc+  2,    0, pv+10, 1, 0,    0},
/*  8: pci* at pcibr*|ppb* bus -1 */
    {&pci_ca,		&pci_cd,	 0, STAR, loc+  1,    0, pv+ 0, 3, 0,    0},
/*  9: pciide* at pci* dev -1 function -1 */
    {&pciide_ca,	&pciide_cd,	 0, STAR, loc+  0,    0, pv+ 6, 7, 0,    0},
/* 10: ppb* at pci* dev -1 function -1 */
    {&ppb_ca,		&ppb_cd,	 0, STAR, loc+  0,    0, pv+ 6, 7, 0,    0},
/* 11: ahci* at pci* dev -1 function -1 */
    {&ahci_ca,		&ahci_cd,	 0, STAR, loc+  0,    0, pv+ 6, 7, 0,    0},
/* 12: ahci_sd* at ahci* */
    {&ahci_sd_ca,	&ahci_sd_cd,	 0, STAR,     loc,    0, pv+14, 9, 0,    0},
/* 13: ahci_cdrom* at ahci* */
    {&ahci_cdrom_ca,	&ahci_cdrom_cd,	 0, STAR,     loc,    0, pv+14, 9, 0,    0},
/* 14: lohci1 at localbus0 base 0x14030000 */
    {&lohci_ca,		&lohci_cd,	 1, NORM, loc+  3,    0, pv+10, 1, 0,    1},
/* 15: lohci0 at localbus0 base 0x14038000 */
    {&lohci_ca,		&lohci_cd,	 0, NORM, loc+  4,    0, pv+10, 1, 0,    0},
/* 16: wd* at pciide* channel -1 drive -1 */
    {&wd_ca,		&wd_cd,		 0, STAR, loc+  0,    0, pv+ 8, 10, 0,    0},
    {0},
    {0},
    {0},
    {0},
    {0},
    {0},
    {0},
    {0},
    {(struct cfattach *)-1}
};

short cfroots[] = {
	 0 /* mainbus0 */,
	-1
};

int cfroots_size = 2;

/* pseudo-devices */
extern void loopattach (int);

char *pdevnames[] = {
	"loop",
};

int pdevnames_size = 1;

struct pdevinit pdevinit[] = {
	{ loopattach, 1 },
	{ 0, 0 }
};
#endif /* NMAINBUS */
